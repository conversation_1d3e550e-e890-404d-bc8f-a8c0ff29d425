using System;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using UnityEngine;

public class JigsawPanel : Panel
{
    public JigsawPanel()
    {
        packName = "Jigsaw";
        compName = "JigsawPanel";
    }

    private OperationLayer oprationLayer;
    private GList listStorage;
    private GComponent oprationComponent;
    private GComponent dragLayer;
    private int imageIndex;

    // 组管理
    private List<JigsawGroup> groups = new List<JigsawGroup>();
    protected override void DoInitialize()
    {
        UIObjectFactory.SetPackageItemExtension($"ui://{packName}/JigsawPiece", () => { return new JigsawPiece(); });

        oprationComponent = contentPane.GetChild("oprationLayer").asCom;
        oprationLayer = new OperationLayer(oprationComponent);
        listStorage = contentPane.GetChild("listStorage").asList;

        // 创建并添加拖拽层
        dragLayer = new GComponent();
        dragLayer.name = "DragLayer";
        // contentPane.fairyBatching = false;
        contentPane.AddChild(dragLayer);
        // 确保拖拽层在最上层
        contentPane.SetChildIndex(dragLayer, contentPane.numChildren - 1);

        listStorage.itemRenderer = UpdateStoragePiece;

        SetData();
    }

    public void SetData()
    {
        imageIndex = 1;
        FUILoader.LoadPackage($"Z_Image_{imageIndex}",()=>
        {
            if(contentPane == null || contentPane.isDisposed) return;
            listStorage.numItems = 6 * 8;
        });
    }

    private void UpdateStoragePiece(int index, GObject item)
    {
        var piece = item as JigsawPiece;
        piece.SetPiece(imageIndex, index);

        // 设置父面板引用，用于拖拽处理
        piece.SetParentPanel(this);
    }

    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        oprationLayer?.SetGridVisible(visible);
    }

    /// <summary>
    /// 更新网格配置
    /// </summary>
    /// <param name="columns">列数</param>
    /// <param name="rows">行数</param>
    /// <param name="lineColor">线条颜色</param>
    /// <param name="lineWidth">线条宽度</param>
    public void UpdateGridConfig(int columns, int rows, Color lineColor, float lineWidth)
    {
        oprationLayer?.UpdateGridConfig(columns, rows, lineColor, lineWidth);
    }

    /// <summary>
    /// 获取操作层的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        return oprationLayer?.GetGridPosition(localPosition) ?? Vector2Int.zero;
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        return oprationLayer?.GetLocalPosition(gridPosition) ?? Vector2.zero;
    }

    /// <summary>
    /// 将全局坐标转换为操作层的本地坐标
    /// </summary>
    /// <param name="globalPosition">全局坐标</param>
    /// <returns>操作层本地坐标</returns>
    public Vector2 GlobalToOperationLayerLocal(Vector2 globalPosition)
    {
        return oprationComponent.GlobalToLocal(globalPosition);
    }

    /// <summary>
    /// 将操作层本地坐标转换为全局坐标
    /// </summary>
    /// <param name="localPosition">操作层本地坐标</param>
    /// <returns>全局坐标</returns>
    public Vector2 OperationLayerLocalToGlobal(Vector2 localPosition)
    {
        return oprationComponent.LocalToGlobal(localPosition);
    }

    /// <summary>
    /// 检查位置是否在操作层范围内
    /// </summary>
    /// <param name="operationLayerLocalPos">操作层本地坐标</param>
    /// <returns>是否在范围内</returns>
    public bool IsPositionInOperationLayer(Vector2 operationLayerLocalPos)
    {
        return operationLayerLocalPos.x >= 0 && operationLayerLocalPos.y >= 0 &&
               operationLayerLocalPos.x <= oprationComponent.width &&
               operationLayerLocalPos.y <= oprationComponent.height;
    }

    /// <summary>
    /// 获取内容面板，用于拖拽时的父容器切换
    /// </summary>
    /// <returns>内容面板</returns>
    public GComponent GetContentPane()
    {
        return contentPane;
    }

    /// <summary>
    /// 获取操作层对象
    /// </summary>
    /// <returns>操作层对象</returns>
    public OperationLayer GetOperationLayer()
    {
        return oprationLayer;
    }

    /// <summary>
    /// 获取拖拽层容器，确保拖拽的拼块在最顶层
    /// </summary>
    /// <returns>拖拽层容器</returns>
    public GComponent GetDragLayer()
    {
        // 返回专用的拖拽层
        return dragLayer;
    }

    /// <summary>
    /// 检查两个拼块是否同时满足原图相邻和操作区域格子相邻
    /// </summary>
    /// <param name="piece1">拼块1</param>
    /// <param name="piece2">拼块2</param>
    /// <returns>是否同时满足两种相邻条件</returns>
    private bool IsAdjacentInBothOriginalAndOperation(JigsawPiece piece1, JigsawPiece piece2)
    {
        if (piece1 == null || piece2 == null) return false;

        // 首先检查原图中是否相邻
        if (!piece1.IsAdjacentTo(piece2)) return false;

        // 然后检查在操作区域格子中是否相邻
        Vector2 piece1Center = piece1.LocalToGlobal(new Vector2(piece1.width * 0.5f, piece1.height * 0.5f));
        Vector2 piece2Center = piece2.LocalToGlobal(new Vector2(piece2.width * 0.5f, piece2.height * 0.5f));

        Vector2 piece1OperationPos = GlobalToOperationLayerLocal(piece1Center);
        Vector2 piece2OperationPos = GlobalToOperationLayerLocal(piece2Center);

        Vector2Int piece1GridPos = GetGridPosition(piece1OperationPos);
        Vector2Int piece2GridPos = GetGridPosition(piece2OperationPos);

        return JigsawGroup.IsAdjacent(piece1GridPos, piece2GridPos);
    }

    /// <summary>
    /// 检查并创建拼块组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    public void CheckAndCreateGroups(JigsawPiece newPiece)
    {
        if (newPiece == null) return;

        // 获取操作层中的所有拼块
        var operationPieces = GetPiecesInOperationLayer();

        // 查找与新拼块相邻的拼块（需要同时满足原图相邻和操作区域格子相邻）
        var adjacentPieces = new List<JigsawPiece>();
        foreach (var piece in operationPieces)
        {
            if (piece != newPiece && IsAdjacentInBothOriginalAndOperation(newPiece, piece))
            {
                adjacentPieces.Add(piece);
            }
        }

        if (adjacentPieces.Count == 0)
        {
            // 没有相邻拼块，不需要创建组
            return;
        }

        // 检查相邻拼块是否已经属于某个组
        var existingGroups = new HashSet<JigsawGroup>();
        foreach (var piece in adjacentPieces)
        {
            var group = piece.GetGroup();
            if (group != null)
            {
                existingGroups.Add(group);
            }
        }

        if (existingGroups.Count == 0)
        {
            // 没有现有组，创建新组
            var newGroup = new JigsawGroup(this);
            newGroup.AddPiece(newPiece);
            foreach (var piece in adjacentPieces)
            {
                newGroup.AddPiece(piece);
            }
            groups.Add(newGroup);
        }
        else if (existingGroups.Count == 1)
        {
            // 有一个现有组，将新拼块和其他相邻拼块加入该组
            var existingGroup = existingGroups.First();
            existingGroup.AddPiece(newPiece);
            foreach (var piece in adjacentPieces)
            {
                if (piece.GetGroup() == null)
                {
                    existingGroup.AddPiece(piece);
                }
            }
        }
        else
        {
            // 有多个现有组，需要合并
            var primaryGroup = existingGroups.First();
            primaryGroup.AddPiece(newPiece);

            // 合并其他组到主组
            var groupsToRemove = new List<JigsawGroup>();
            foreach (var group in existingGroups.Skip(1))
            {
                primaryGroup.MergeWith(group);
                groupsToRemove.Add(group);
            }

            // 移除已合并的组
            foreach (var group in groupsToRemove)
            {
                groups.Remove(group);
                group.Dispose();
            }

            // 添加没有组的相邻拼块
            foreach (var piece in adjacentPieces)
            {
                if (piece.GetGroup() == null)
                {
                    primaryGroup.AddPiece(piece);
                }
            }
        }
    }

    /// <summary>
    /// 获取操作层中的所有拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    private List<JigsawPiece> GetPiecesInOperationLayer()
    {
        var pieces = new List<JigsawPiece>();

        // 遍历拖拽层中的所有子对象
        for (int i = 0; i < dragLayer.numChildren; i++)
        {
            var child = dragLayer.GetChildAt(i);
            if (child is JigsawPiece piece)
            {
                // 检查拼块是否在操作层范围内
                Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
                Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
                Vector2 operationLayerLocalPos = GlobalToOperationLayerLocal(globalCenterPos);

                if (IsPositionInOperationLayer(operationLayerLocalPos))
                {
                    pieces.Add(piece);
                }
            }
        }

        return pieces;
    }

    /// <summary>
    /// 移除组
    /// </summary>
    /// <param name="group">要移除的组</param>
    public void RemoveGroup(JigsawGroup group)
    {
        if (group != null && groups.Contains(group))
        {
            groups.Remove(group);
            group.Dispose();
        }
    }

    /// <summary>
    /// 清理所有组
    /// </summary>
    public void ClearAllGroups()
    {
        foreach (var group in groups)
        {
            group.Dispose();
        }
        groups.Clear();
    }
}
